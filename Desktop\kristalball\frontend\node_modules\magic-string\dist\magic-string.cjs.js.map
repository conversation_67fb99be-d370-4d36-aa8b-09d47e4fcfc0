{"version": 3, "file": "magic-string.cjs.js", "sources": ["../src/BitSet.js", "../src/Chunk.js", "../src/SourceMap.js", "../src/utils/guessIndent.js", "../src/utils/getRelativePath.js", "../src/utils/isObject.js", "../src/utils/getLocator.js", "../src/utils/Mappings.js", "../src/MagicString.js", "../src/Bundle.js", "../src/index-legacy.js"], "sourcesContent": ["export default class BitSet {\n\tconstructor(arg) {\n\t\tthis.bits = arg instanceof BitSet ? arg.bits.slice() : [];\n\t}\n\n\tadd(n) {\n\t\tthis.bits[n >> 5] |= 1 << (n & 31);\n\t}\n\n\thas(n) {\n\t\treturn !!(this.bits[n >> 5] & (1 << (n & 31)));\n\t}\n}\n", "export default class Chunk {\n\tconstructor(start, end, content) {\n\t\tthis.start = start;\n\t\tthis.end = end;\n\t\tthis.original = content;\n\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\n\t\tthis.content = content;\n\t\tthis.storeName = false;\n\t\tthis.edited = false;\n\n\t\tif (DEBUG) {\n\t\t\t// we make these non-enumerable, for sanity while debugging\n\t\t\tObject.defineProperties(this, {\n\t\t\t\tprevious: { writable: true, value: null },\n\t\t\t\tnext: { writable: true, value: null },\n\t\t\t});\n\t\t} else {\n\t\t\tthis.previous = null;\n\t\t\tthis.next = null;\n\t\t}\n\t}\n\n\tappendLeft(content) {\n\t\tthis.outro += content;\n\t}\n\n\tappendRight(content) {\n\t\tthis.intro = this.intro + content;\n\t}\n\n\tclone() {\n\t\tconst chunk = new Chunk(this.start, this.end, this.original);\n\n\t\tchunk.intro = this.intro;\n\t\tchunk.outro = this.outro;\n\t\tchunk.content = this.content;\n\t\tchunk.storeName = this.storeName;\n\t\tchunk.edited = this.edited;\n\n\t\treturn chunk;\n\t}\n\n\tcontains(index) {\n\t\treturn this.start < index && index < this.end;\n\t}\n\n\teachNext(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.next;\n\t\t}\n\t}\n\n\teachPrevious(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.previous;\n\t\t}\n\t}\n\n\tedit(content, storeName, contentOnly) {\n\t\tthis.content = content;\n\t\tif (!contentOnly) {\n\t\t\tthis.intro = '';\n\t\t\tthis.outro = '';\n\t\t}\n\t\tthis.storeName = storeName;\n\n\t\tthis.edited = true;\n\n\t\treturn this;\n\t}\n\n\tprependLeft(content) {\n\t\tthis.outro = content + this.outro;\n\t}\n\n\tprependRight(content) {\n\t\tthis.intro = content + this.intro;\n\t}\n\n\treset() {\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\t\tif (this.edited) {\n\t\t\tthis.content = this.original;\n\t\t\tthis.storeName = false;\n\t\t\tthis.edited = false;\n\t\t}\n\t}\n\n\tsplit(index) {\n\t\tconst sliceIndex = index - this.start;\n\n\t\tconst originalBefore = this.original.slice(0, sliceIndex);\n\t\tconst originalAfter = this.original.slice(sliceIndex);\n\n\t\tthis.original = originalBefore;\n\n\t\tconst newChunk = new Chunk(index, this.end, originalAfter);\n\t\tnewChunk.outro = this.outro;\n\t\tthis.outro = '';\n\n\t\tthis.end = index;\n\n\t\tif (this.edited) {\n\t\t\t// after split we should save the edit content record into the correct chunk\n\t\t\t// to make sure sourcemap correct\n\t\t\t// For example:\n\t\t\t// '  test'.trim()\n\t\t\t//     split   -> '  ' + 'test'\n\t\t\t//   ✔️ edit    -> '' + 'test'\n\t\t\t//   ✖️ edit    -> 'test' + ''\n\t\t\t// TODO is this block necessary?...\n\t\t\tnewChunk.edit('', false);\n\t\t\tthis.content = '';\n\t\t} else {\n\t\t\tthis.content = originalBefore;\n\t\t}\n\n\t\tnewChunk.next = this.next;\n\t\tif (newChunk.next) newChunk.next.previous = newChunk;\n\t\tnewChunk.previous = this;\n\t\tthis.next = newChunk;\n\n\t\treturn newChunk;\n\t}\n\n\ttoString() {\n\t\treturn this.intro + this.content + this.outro;\n\t}\n\n\ttrimEnd(rx) {\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.start + trimmed.length).edit('', undefined, true);\n\t\t\t\tif (this.edited) {\n\t\t\t\t\t// save the change, if it has been edited\n\t\t\t\t\tthis.edit(trimmed, this.storeName, true);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\tif (this.intro.length) return true;\n\t\t}\n\t}\n\n\ttrimStart(rx) {\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tconst newChunk = this.split(this.end - trimmed.length);\n\t\t\t\tif (this.edited) {\n\t\t\t\t\t// save the change, if it has been edited\n\t\t\t\t\tnewChunk.edit(trimmed, this.storeName, true);\n\t\t\t\t}\n\t\t\t\tthis.edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.outro = this.outro.replace(rx, '');\n\t\t\tif (this.outro.length) return true;\n\t\t}\n\t}\n}\n", "import { encode } from '@jridgewell/sourcemap-codec';\n\nfunction getBtoa() {\n\tif (typeof globalThis !== 'undefined' && typeof globalThis.btoa === 'function') {\n\t\treturn (str) => globalThis.btoa(unescape(encodeURIComponent(str)));\n\t} else if (typeof Buffer === 'function') {\n\t\treturn (str) => Buffer.from(str, 'utf-8').toString('base64');\n\t} else {\n\t\treturn () => {\n\t\t\tthrow new Error('Unsupported environment: `window.btoa` or `Buffer` should be supported.');\n\t\t};\n\t}\n}\n\nconst btoa = /*#__PURE__*/ getBtoa();\n\nexport default class SourceMap {\n\tconstructor(properties) {\n\t\tthis.version = 3;\n\t\tthis.file = properties.file;\n\t\tthis.sources = properties.sources;\n\t\tthis.sourcesContent = properties.sourcesContent;\n\t\tthis.names = properties.names;\n\t\tthis.mappings = encode(properties.mappings);\n\t\tif (typeof properties.x_google_ignoreList !== 'undefined') {\n\t\t\tthis.x_google_ignoreList = properties.x_google_ignoreList;\n\t\t}\n\t\tif (typeof properties.debugId !== 'undefined') {\n\t\t\tthis.debugId = properties.debugId;\n\t\t}\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this);\n\t}\n\n\ttoUrl() {\n\t\treturn 'data:application/json;charset=utf-8;base64,' + btoa(this.toString());\n\t}\n}\n", "export default function guessIndent(code) {\n\tconst lines = code.split('\\n');\n\n\tconst tabbed = lines.filter((line) => /^\\t+/.test(line));\n\tconst spaced = lines.filter((line) => /^ {2,}/.test(line));\n\n\tif (tabbed.length === 0 && spaced.length === 0) {\n\t\treturn null;\n\t}\n\n\t// More lines tabbed than spaced? Assume tabs, and\n\t// default to tabs in the case of a tie (or nothing\n\t// to go on)\n\tif (tabbed.length >= spaced.length) {\n\t\treturn '\\t';\n\t}\n\n\t// Otherwise, we need to guess the multiple\n\tconst min = spaced.reduce((previous, current) => {\n\t\tconst numSpaces = /^ +/.exec(current)[0].length;\n\t\treturn Math.min(numSpaces, previous);\n\t}, Infinity);\n\n\treturn new Array(min + 1).join(' ');\n}\n", "export default function getRelativePath(from, to) {\n\tconst fromParts = from.split(/[/\\\\]/);\n\tconst toParts = to.split(/[/\\\\]/);\n\n\tfromParts.pop(); // get dirname\n\n\twhile (fromParts[0] === toParts[0]) {\n\t\tfromParts.shift();\n\t\ttoParts.shift();\n\t}\n\n\tif (fromParts.length) {\n\t\tlet i = fromParts.length;\n\t\twhile (i--) fromParts[i] = '..';\n\t}\n\n\treturn fromParts.concat(toParts).join('/');\n}\n", "const toString = Object.prototype.toString;\n\nexport default function isObject(thing) {\n\treturn toString.call(thing) === '[object Object]';\n}\n", "export default function getLocator(source) {\n\tconst originalLines = source.split('\\n');\n\tconst lineOffsets = [];\n\n\tfor (let i = 0, pos = 0; i < originalLines.length; i++) {\n\t\tlineOffsets.push(pos);\n\t\tpos += originalLines[i].length + 1;\n\t}\n\n\treturn function locate(index) {\n\t\tlet i = 0;\n\t\tlet j = lineOffsets.length;\n\t\twhile (i < j) {\n\t\t\tconst m = (i + j) >> 1;\n\t\t\tif (index < lineOffsets[m]) {\n\t\t\t\tj = m;\n\t\t\t} else {\n\t\t\t\ti = m + 1;\n\t\t\t}\n\t\t}\n\t\tconst line = i - 1;\n\t\tconst column = index - lineOffsets[line];\n\t\treturn { line, column };\n\t};\n}\n", "const wordRegex = /\\w/;\n\nexport default class Mappings {\n\tconstructor(hires) {\n\t\tthis.hires = hires;\n\t\tthis.generatedCodeLine = 0;\n\t\tthis.generatedCodeColumn = 0;\n\t\tthis.raw = [];\n\t\tthis.rawSegments = this.raw[this.generatedCodeLine] = [];\n\t\tthis.pending = null;\n\t}\n\n\taddEdit(sourceIndex, content, loc, nameIndex) {\n\t\tif (content.length) {\n\t\t\tconst contentLengthMinusOne = content.length - 1;\n\t\t\tlet contentLineEnd = content.indexOf('\\n', 0);\n\t\t\tlet previousContentLineEnd = -1;\n\t\t\t// Loop through each line in the content and add a segment, but stop if the last line is empty,\n\t\t\t// else code afterwards would fill one line too many\n\t\t\twhile (contentLineEnd >= 0 && contentLengthMinusOne > contentLineEnd) {\n\t\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\t\tif (nameIndex >= 0) {\n\t\t\t\t\tsegment.push(nameIndex);\n\t\t\t\t}\n\t\t\t\tthis.rawSegments.push(segment);\n\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\n\t\t\t\tpreviousContentLineEnd = contentLineEnd;\n\t\t\t\tcontentLineEnd = content.indexOf('\\n', contentLineEnd + 1);\n\t\t\t}\n\n\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\tif (nameIndex >= 0) {\n\t\t\t\tsegment.push(nameIndex);\n\t\t\t}\n\t\t\tthis.rawSegments.push(segment);\n\n\t\t\tthis.advance(content.slice(previousContentLineEnd + 1));\n\t\t} else if (this.pending) {\n\t\t\tthis.rawSegments.push(this.pending);\n\t\t\tthis.advance(content);\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\taddUneditedChunk(sourceIndex, chunk, original, loc, sourcemapLocations) {\n\t\tlet originalCharIndex = chunk.start;\n\t\tlet first = true;\n\t\t// when iterating each char, check if it's in a word boundary\n\t\tlet charInHiresBoundary = false;\n\n\t\twhile (originalCharIndex < chunk.end) {\n\t\t\tif (original[originalCharIndex] === '\\n') {\n\t\t\t\tloc.line += 1;\n\t\t\t\tloc.column = 0;\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\t\t\t\tfirst = true;\n\t\t\t\tcharInHiresBoundary = false;\n\t\t\t} else {\n\t\t\t\tif (this.hires || first || sourcemapLocations.has(originalCharIndex)) {\n\t\t\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\n\t\t\t\t\tif (this.hires === 'boundary') {\n\t\t\t\t\t\t// in hires \"boundary\", group segments per word boundary than per char\n\t\t\t\t\t\tif (wordRegex.test(original[originalCharIndex])) {\n\t\t\t\t\t\t\t// for first char in the boundary found, start the boundary by pushing a segment\n\t\t\t\t\t\t\tif (!charInHiresBoundary) {\n\t\t\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t\t\t\tcharInHiresBoundary = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// for non-word char, end the boundary by pushing a segment\n\t\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t\t\tcharInHiresBoundary = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tloc.column += 1;\n\t\t\t\tthis.generatedCodeColumn += 1;\n\t\t\t\tfirst = false;\n\t\t\t}\n\n\t\t\toriginalCharIndex += 1;\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\tadvance(str) {\n\t\tif (!str) return;\n\n\t\tconst lines = str.split('\\n');\n\n\t\tif (lines.length > 1) {\n\t\t\tfor (let i = 0; i < lines.length - 1; i++) {\n\t\t\t\tthis.generatedCodeLine++;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t}\n\t\t\tthis.generatedCodeColumn = 0;\n\t\t}\n\n\t\tthis.generatedCodeColumn += lines[lines.length - 1].length;\n\t}\n}\n", "import BitSet from './BitSet.js';\nimport Chunk from './Chunk.js';\nimport SourceMap from './SourceMap.js';\nimport guessIndent from './utils/guessIndent.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\nimport Stats from './utils/Stats.js';\n\nconst n = '\\n';\n\nconst warned = {\n\tinsertLeft: false,\n\tinsertRight: false,\n\tstoreName: false,\n};\n\nexport default class MagicString {\n\tconstructor(string, options = {}) {\n\t\tconst chunk = new Chunk(0, string.length, string);\n\n\t\tObject.defineProperties(this, {\n\t\t\toriginal: { writable: true, value: string },\n\t\t\toutro: { writable: true, value: '' },\n\t\t\tintro: { writable: true, value: '' },\n\t\t\tfirstChunk: { writable: true, value: chunk },\n\t\t\tlastChunk: { writable: true, value: chunk },\n\t\t\tlastSearchedChunk: { writable: true, value: chunk },\n\t\t\tbyStart: { writable: true, value: {} },\n\t\t\tbyEnd: { writable: true, value: {} },\n\t\t\tfilename: { writable: true, value: options.filename },\n\t\t\tindentExclusionRanges: { writable: true, value: options.indentExclusionRanges },\n\t\t\tsourcemapLocations: { writable: true, value: new BitSet() },\n\t\t\tstoredNames: { writable: true, value: {} },\n\t\t\tindentStr: { writable: true, value: undefined },\n\t\t\tignoreList: { writable: true, value: options.ignoreList },\n\t\t\toffset: { writable: true, value: options.offset || 0 },\n\t\t});\n\n\t\tif (DEBUG) {\n\t\t\tObject.defineProperty(this, 'stats', { value: new Stats() });\n\t\t}\n\n\t\tthis.byStart[0] = chunk;\n\t\tthis.byEnd[string.length] = chunk;\n\t}\n\n\taddSourcemapLocation(char) {\n\t\tthis.sourcemapLocations.add(char);\n\t}\n\n\tappend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.outro += content;\n\t\treturn this;\n\t}\n\n\tappendLeft(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendLeft');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendLeft(content);\n\t\t} else {\n\t\t\tthis.intro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendLeft');\n\t\treturn this;\n\t}\n\n\tappendRight(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendRight(content);\n\t\t} else {\n\t\t\tthis.outro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendRight');\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst cloned = new MagicString(this.original, { filename: this.filename, offset: this.offset });\n\n\t\tlet originalChunk = this.firstChunk;\n\t\tlet clonedChunk = (cloned.firstChunk = cloned.lastSearchedChunk = originalChunk.clone());\n\n\t\twhile (originalChunk) {\n\t\t\tcloned.byStart[clonedChunk.start] = clonedChunk;\n\t\t\tcloned.byEnd[clonedChunk.end] = clonedChunk;\n\n\t\t\tconst nextOriginalChunk = originalChunk.next;\n\t\t\tconst nextClonedChunk = nextOriginalChunk && nextOriginalChunk.clone();\n\n\t\t\tif (nextClonedChunk) {\n\t\t\t\tclonedChunk.next = nextClonedChunk;\n\t\t\t\tnextClonedChunk.previous = clonedChunk;\n\n\t\t\t\tclonedChunk = nextClonedChunk;\n\t\t\t}\n\n\t\t\toriginalChunk = nextOriginalChunk;\n\t\t}\n\n\t\tcloned.lastChunk = clonedChunk;\n\n\t\tif (this.indentExclusionRanges) {\n\t\t\tcloned.indentExclusionRanges = this.indentExclusionRanges.slice();\n\t\t}\n\n\t\tcloned.sourcemapLocations = new BitSet(this.sourcemapLocations);\n\n\t\tcloned.intro = this.intro;\n\t\tcloned.outro = this.outro;\n\n\t\treturn cloned;\n\t}\n\n\tgenerateDecodedMap(options) {\n\t\toptions = options || {};\n\n\t\tconst sourceIndex = 0;\n\t\tconst names = Object.keys(this.storedNames);\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tconst locate = getLocator(this.original);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.firstChunk.eachNext((chunk) => {\n\t\t\tconst loc = locate(chunk.start);\n\n\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tmappings.addEdit(\n\t\t\t\t\tsourceIndex,\n\t\t\t\t\tchunk.content,\n\t\t\t\t\tloc,\n\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1,\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tmappings.addUneditedChunk(sourceIndex, chunk, this.original, loc, this.sourcemapLocations);\n\t\t\t}\n\n\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : undefined,\n\t\t\tsources: [\n\t\t\t\toptions.source ? getRelativePath(options.file || '', options.source) : options.file || '',\n\t\t\t],\n\t\t\tsourcesContent: options.includeContent ? [this.original] : undefined,\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t\tx_google_ignoreList: this.ignoreList ? [sourceIndex] : undefined,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\t_ensureindentStr() {\n\t\tif (this.indentStr === undefined) {\n\t\t\tthis.indentStr = guessIndent(this.original);\n\t\t}\n\t}\n\n\t_getRawIndentString() {\n\t\tthis._ensureindentStr();\n\t\treturn this.indentStr;\n\t}\n\n\tgetIndentString() {\n\t\tthis._ensureindentStr();\n\t\treturn this.indentStr === null ? '\\t' : this.indentStr;\n\t}\n\n\tindent(indentStr, options) {\n\t\tconst pattern = /^[^\\r\\n]/gm;\n\n\t\tif (isObject(indentStr)) {\n\t\t\toptions = indentStr;\n\t\t\tindentStr = undefined;\n\t\t}\n\n\t\tif (indentStr === undefined) {\n\t\t\tthis._ensureindentStr();\n\t\t\tindentStr = this.indentStr || '\\t';\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\toptions = options || {};\n\n\t\t// Process exclusion ranges\n\t\tconst isExcluded = {};\n\n\t\tif (options.exclude) {\n\t\t\tconst exclusions =\n\t\t\t\ttypeof options.exclude[0] === 'number' ? [options.exclude] : options.exclude;\n\t\t\texclusions.forEach((exclusion) => {\n\t\t\t\tfor (let i = exclusion[0]; i < exclusion[1]; i += 1) {\n\t\t\t\t\tisExcluded[i] = true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet shouldIndentNextCharacter = options.indentStart !== false;\n\t\tconst replacer = (match) => {\n\t\t\tif (shouldIndentNextCharacter) return `${indentStr}${match}`;\n\t\t\tshouldIndentNextCharacter = true;\n\t\t\treturn match;\n\t\t};\n\n\t\tthis.intro = this.intro.replace(pattern, replacer);\n\n\t\tlet charIndex = 0;\n\t\tlet chunk = this.firstChunk;\n\n\t\twhile (chunk) {\n\t\t\tconst end = chunk.end;\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\tchunk.content = chunk.content.replace(pattern, replacer);\n\n\t\t\t\t\tif (chunk.content.length) {\n\t\t\t\t\t\tshouldIndentNextCharacter = chunk.content[chunk.content.length - 1] === '\\n';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcharIndex = chunk.start;\n\n\t\t\t\twhile (charIndex < end) {\n\t\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\t\tconst char = this.original[charIndex];\n\n\t\t\t\t\t\tif (char === '\\n') {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = true;\n\t\t\t\t\t\t} else if (char !== '\\r' && shouldIndentNextCharacter) {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = false;\n\n\t\t\t\t\t\t\tif (charIndex === chunk.start) {\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis._splitChunk(chunk, charIndex);\n\t\t\t\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tcharIndex += 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcharIndex = chunk.end;\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tthis.outro = this.outro.replace(pattern, replacer);\n\n\t\treturn this;\n\t}\n\n\tinsert() {\n\t\tthrow new Error(\n\t\t\t'magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)',\n\t\t);\n\t}\n\n\tinsertLeft(index, content) {\n\t\tif (!warned.insertLeft) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead',\n\t\t\t);\n\t\t\twarned.insertLeft = true;\n\t\t}\n\n\t\treturn this.appendLeft(index, content);\n\t}\n\n\tinsertRight(index, content) {\n\t\tif (!warned.insertRight) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead',\n\t\t\t);\n\t\t\twarned.insertRight = true;\n\t\t}\n\n\t\treturn this.prependRight(index, content);\n\t}\n\n\tmove(start, end, index) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\t\tindex = index + this.offset;\n\n\t\tif (index >= start && index <= end) throw new Error('Cannot move a selection inside itself');\n\n\t\tif (DEBUG) this.stats.time('move');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\t\tthis._split(index);\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tconst oldLeft = first.previous;\n\t\tconst oldRight = last.next;\n\n\t\tconst newRight = this.byStart[index];\n\t\tif (!newRight && last === this.lastChunk) return this;\n\t\tconst newLeft = newRight ? newRight.previous : this.lastChunk;\n\n\t\tif (oldLeft) oldLeft.next = oldRight;\n\t\tif (oldRight) oldRight.previous = oldLeft;\n\n\t\tif (newLeft) newLeft.next = first;\n\t\tif (newRight) newRight.previous = last;\n\n\t\tif (!first.previous) this.firstChunk = last.next;\n\t\tif (!last.next) {\n\t\t\tthis.lastChunk = first.previous;\n\t\t\tthis.lastChunk.next = null;\n\t\t}\n\n\t\tfirst.previous = newLeft;\n\t\tlast.next = newRight || null;\n\n\t\tif (!newLeft) this.firstChunk = first;\n\t\tif (!newRight) this.lastChunk = last;\n\n\t\tif (DEBUG) this.stats.timeEnd('move');\n\t\treturn this;\n\t}\n\n\toverwrite(start, end, content, options) {\n\t\toptions = options || {};\n\t\treturn this.update(start, end, content, { ...options, overwrite: !options.contentOnly });\n\t}\n\n\tupdate(start, end, content, options) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('replacement content must be a string');\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tif (end > this.original.length) throw new Error('end is out of bounds');\n\t\tif (start === end)\n\t\t\tthrow new Error(\n\t\t\t\t'Cannot overwrite a zero-length range – use appendLeft or prependRight instead',\n\t\t\t);\n\n\t\tif (DEBUG) this.stats.time('overwrite');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tif (options === true) {\n\t\t\tif (!warned.storeName) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string',\n\t\t\t\t);\n\t\t\t\twarned.storeName = true;\n\t\t\t}\n\n\t\t\toptions = { storeName: true };\n\t\t}\n\t\tconst storeName = options !== undefined ? options.storeName : false;\n\t\tconst overwrite = options !== undefined ? options.overwrite : false;\n\n\t\tif (storeName) {\n\t\t\tconst original = this.original.slice(start, end);\n\t\t\tObject.defineProperty(this.storedNames, original, {\n\t\t\t\twritable: true,\n\t\t\t\tvalue: true,\n\t\t\t\tenumerable: true,\n\t\t\t});\n\t\t}\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tif (first) {\n\t\t\tlet chunk = first;\n\t\t\twhile (chunk !== last) {\n\t\t\t\tif (chunk.next !== this.byStart[chunk.end]) {\n\t\t\t\t\tthrow new Error('Cannot overwrite across a split point');\n\t\t\t\t}\n\t\t\t\tchunk = chunk.next;\n\t\t\t\tchunk.edit('', false);\n\t\t\t}\n\n\t\t\tfirst.edit(content, storeName, !overwrite);\n\t\t} else {\n\t\t\t// must be inserting at the end\n\t\t\tconst newChunk = new Chunk(start, end, '').edit(content, storeName);\n\n\t\t\t// TODO last chunk in the array may not be the last chunk, if it's moved...\n\t\t\tlast.next = newChunk;\n\t\t\tnewChunk.previous = last;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('overwrite');\n\t\treturn this;\n\t}\n\n\tprepend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.intro = content + this.intro;\n\t\treturn this;\n\t}\n\n\tprependLeft(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependLeft(content);\n\t\t} else {\n\t\t\tthis.intro = content + this.intro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tprependRight(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependRight(content);\n\t\t} else {\n\t\t\tthis.outro = content + this.outro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tremove(start, end) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('remove');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.intro = '';\n\t\t\tchunk.outro = '';\n\t\t\tchunk.edit('');\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('remove');\n\t\treturn this;\n\t}\n\n\treset(start, end) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('reset');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.reset();\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('reset');\n\t\treturn this;\n\t}\n\n\tlastChar() {\n\t\tif (this.outro.length) return this.outro[this.outro.length - 1];\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length) return chunk.outro[chunk.outro.length - 1];\n\t\t\tif (chunk.content.length) return chunk.content[chunk.content.length - 1];\n\t\t\tif (chunk.intro.length) return chunk.intro[chunk.intro.length - 1];\n\t\t} while ((chunk = chunk.previous));\n\t\tif (this.intro.length) return this.intro[this.intro.length - 1];\n\t\treturn '';\n\t}\n\n\tlastLine() {\n\t\tlet lineIndex = this.outro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.outro.substr(lineIndex + 1);\n\t\tlet lineStr = this.outro;\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length > 0) {\n\t\t\t\tlineIndex = chunk.outro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.outro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.outro + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.content.length > 0) {\n\t\t\t\tlineIndex = chunk.content.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.content.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.content + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.intro.length > 0) {\n\t\t\t\tlineIndex = chunk.intro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.intro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.intro + lineStr;\n\t\t\t}\n\t\t} while ((chunk = chunk.previous));\n\t\tlineIndex = this.intro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.intro.substr(lineIndex + 1) + lineStr;\n\t\treturn this.intro + lineStr;\n\t}\n\n\tslice(start = 0, end = this.original.length - this.offset) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tlet result = '';\n\n\t\t// find start chunk\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk && (chunk.start > start || chunk.end <= start)) {\n\t\t\t// found end chunk before start\n\t\t\tif (chunk.start < end && chunk.end >= end) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tif (chunk && chunk.edited && chunk.start !== start)\n\t\t\tthrow new Error(`Cannot use replaced character ${start} as slice start anchor.`);\n\n\t\tconst startChunk = chunk;\n\t\twhile (chunk) {\n\t\t\tif (chunk.intro && (startChunk !== chunk || chunk.start === start)) {\n\t\t\t\tresult += chunk.intro;\n\t\t\t}\n\n\t\t\tconst containsEnd = chunk.start < end && chunk.end >= end;\n\t\t\tif (containsEnd && chunk.edited && chunk.end !== end)\n\t\t\t\tthrow new Error(`Cannot use replaced character ${end} as slice end anchor.`);\n\n\t\t\tconst sliceStart = startChunk === chunk ? start - chunk.start : 0;\n\t\t\tconst sliceEnd = containsEnd ? chunk.content.length + end - chunk.end : chunk.content.length;\n\n\t\t\tresult += chunk.content.slice(sliceStart, sliceEnd);\n\n\t\t\tif (chunk.outro && (!containsEnd || chunk.end === end)) {\n\t\t\t\tresult += chunk.outro;\n\t\t\t}\n\n\t\t\tif (containsEnd) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t// TODO deprecate this? not really very useful\n\tsnip(start, end) {\n\t\tconst clone = this.clone();\n\t\tclone.remove(0, start);\n\t\tclone.remove(end, clone.original.length);\n\n\t\treturn clone;\n\t}\n\n\t_split(index) {\n\t\tif (this.byStart[index] || this.byEnd[index]) return;\n\n\t\tif (DEBUG) this.stats.time('_split');\n\n\t\tlet chunk = this.lastSearchedChunk;\n\t\tconst searchForward = index > chunk.end;\n\n\t\twhile (chunk) {\n\t\t\tif (chunk.contains(index)) return this._splitChunk(chunk, index);\n\n\t\t\tchunk = searchForward ? this.byStart[chunk.end] : this.byEnd[chunk.start];\n\t\t}\n\t}\n\n\t_splitChunk(chunk, index) {\n\t\tif (chunk.edited && chunk.content.length) {\n\t\t\t// zero-length edited chunks are a special case (overlapping replacements)\n\t\t\tconst loc = getLocator(this.original)(index);\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot split a chunk that has already been edited (${loc.line}:${loc.column} – \"${chunk.original}\")`,\n\t\t\t);\n\t\t}\n\n\t\tconst newChunk = chunk.split(index);\n\n\t\tthis.byEnd[index] = chunk;\n\t\tthis.byStart[index] = newChunk;\n\t\tthis.byEnd[newChunk.end] = newChunk;\n\n\t\tif (chunk === this.lastChunk) this.lastChunk = newChunk;\n\n\t\tthis.lastSearchedChunk = chunk;\n\t\tif (DEBUG) this.stats.timeEnd('_split');\n\t\treturn true;\n\t}\n\n\ttoString() {\n\t\tlet str = this.intro;\n\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk) {\n\t\t\tstr += chunk.toString();\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn str + this.outro;\n\t}\n\n\tisEmpty() {\n\t\tlet chunk = this.firstChunk;\n\t\tdo {\n\t\t\tif (\n\t\t\t\t(chunk.intro.length && chunk.intro.trim()) ||\n\t\t\t\t(chunk.content.length && chunk.content.trim()) ||\n\t\t\t\t(chunk.outro.length && chunk.outro.trim())\n\t\t\t)\n\t\t\t\treturn false;\n\t\t} while ((chunk = chunk.next));\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\tlet chunk = this.firstChunk;\n\t\tlet length = 0;\n\t\tdo {\n\t\t\tlength += chunk.intro.length + chunk.content.length + chunk.outro.length;\n\t\t} while ((chunk = chunk.next));\n\t\treturn length;\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimEndAborted(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tlet chunk = this.lastChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimEnd(rx);\n\n\t\t\t// if chunk was trimmed, we have a new lastChunk\n\t\t\tif (chunk.end !== end) {\n\t\t\t\tif (this.lastChunk === chunk) {\n\t\t\t\t\tthis.lastChunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.previous;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimEnd(charType) {\n\t\tthis.trimEndAborted(charType);\n\t\treturn this;\n\t}\n\ttrimStartAborted(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tlet chunk = this.firstChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimStart(rx);\n\n\t\t\tif (chunk.end !== end) {\n\t\t\t\t// special case...\n\t\t\t\tif (chunk === this.lastChunk) this.lastChunk = chunk.next;\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.next;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimStart(charType) {\n\t\tthis.trimStartAborted(charType);\n\t\treturn this;\n\t}\n\n\thasChanged() {\n\t\treturn this.original !== this.toString();\n\t}\n\n\t_replaceRegexp(searchValue, replacement) {\n\t\tfunction getReplacement(match, str) {\n\t\t\tif (typeof replacement === 'string') {\n\t\t\t\treturn replacement.replace(/\\$(\\$|&|\\d+)/g, (_, i) => {\n\t\t\t\t\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace#specifying_a_string_as_a_parameter\n\t\t\t\t\tif (i === '$') return '$';\n\t\t\t\t\tif (i === '&') return match[0];\n\t\t\t\t\tconst num = +i;\n\t\t\t\t\tif (num < match.length) return match[+i];\n\t\t\t\t\treturn `$${i}`;\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\treturn replacement(...match, match.index, str, match.groups);\n\t\t\t}\n\t\t}\n\t\tfunction matchAll(re, str) {\n\t\t\tlet match;\n\t\t\tconst matches = [];\n\t\t\twhile ((match = re.exec(str))) {\n\t\t\t\tmatches.push(match);\n\t\t\t}\n\t\t\treturn matches;\n\t\t}\n\t\tif (searchValue.global) {\n\t\t\tconst matches = matchAll(searchValue, this.original);\n\t\t\tmatches.forEach((match) => {\n\t\t\t\tif (match.index != null) {\n\t\t\t\t\tconst replacement = getReplacement(match, this.original);\n\t\t\t\t\tif (replacement !== match[0]) {\n\t\t\t\t\t\tthis.overwrite(match.index, match.index + match[0].length, replacement);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tconst match = this.original.match(searchValue);\n\t\t\tif (match && match.index != null) {\n\t\t\t\tconst replacement = getReplacement(match, this.original);\n\t\t\t\tif (replacement !== match[0]) {\n\t\t\t\t\tthis.overwrite(match.index, match.index + match[0].length, replacement);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\t_replaceString(string, replacement) {\n\t\tconst { original } = this;\n\t\tconst index = original.indexOf(string);\n\n\t\tif (index !== -1) {\n\t\t\tthis.overwrite(index, index + string.length, replacement);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\treplace(searchValue, replacement) {\n\t\tif (typeof searchValue === 'string') {\n\t\t\treturn this._replaceString(searchValue, replacement);\n\t\t}\n\n\t\treturn this._replaceRegexp(searchValue, replacement);\n\t}\n\n\t_replaceAllString(string, replacement) {\n\t\tconst { original } = this;\n\t\tconst stringLength = string.length;\n\t\tfor (\n\t\t\tlet index = original.indexOf(string);\n\t\t\tindex !== -1;\n\t\t\tindex = original.indexOf(string, index + stringLength)\n\t\t) {\n\t\t\tconst previous = original.slice(index, index + stringLength);\n\t\t\tif (previous !== replacement) this.overwrite(index, index + stringLength, replacement);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\treplaceAll(searchValue, replacement) {\n\t\tif (typeof searchValue === 'string') {\n\t\t\treturn this._replaceAllString(searchValue, replacement);\n\t\t}\n\n\t\tif (!searchValue.global) {\n\t\t\tthrow new TypeError(\n\t\t\t\t'MagicString.prototype.replaceAll called with a non-global RegExp argument',\n\t\t\t);\n\t\t}\n\n\t\treturn this._replaceRegexp(searchValue, replacement);\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport SourceMap from './SourceMap.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\n\nconst hasOwnProp = Object.prototype.hasOwnProperty;\n\nexport default class Bundle {\n\tconstructor(options = {}) {\n\t\tthis.intro = options.intro || '';\n\t\tthis.separator = options.separator !== undefined ? options.separator : '\\n';\n\t\tthis.sources = [];\n\t\tthis.uniqueSources = [];\n\t\tthis.uniqueSourceIndexByFilename = {};\n\t}\n\n\taddSource(source) {\n\t\tif (source instanceof MagicString) {\n\t\t\treturn this.addSource({\n\t\t\t\tcontent: source,\n\t\t\t\tfilename: source.filename,\n\t\t\t\tseparator: this.separator,\n\t\t\t});\n\t\t}\n\n\t\tif (!isObject(source) || !source.content) {\n\t\t\tthrow new Error(\n\t\t\t\t'bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`',\n\t\t\t);\n\t\t}\n\n\t\t['filename', 'ignoreList', 'indentExclusionRanges', 'separator'].forEach((option) => {\n\t\t\tif (!hasOwnProp.call(source, option)) source[option] = source.content[option];\n\t\t});\n\n\t\tif (source.separator === undefined) {\n\t\t\t// TODO there's a bunch of this sort of thing, needs cleaning up\n\t\t\tsource.separator = this.separator;\n\t\t}\n\n\t\tif (source.filename) {\n\t\t\tif (!hasOwnProp.call(this.uniqueSourceIndexByFilename, source.filename)) {\n\t\t\t\tthis.uniqueSourceIndexByFilename[source.filename] = this.uniqueSources.length;\n\t\t\t\tthis.uniqueSources.push({ filename: source.filename, content: source.content.original });\n\t\t\t} else {\n\t\t\t\tconst uniqueSource = this.uniqueSources[this.uniqueSourceIndexByFilename[source.filename]];\n\t\t\t\tif (source.content.original !== uniqueSource.content) {\n\t\t\t\t\tthrow new Error(`Illegal source: same filename (${source.filename}), different contents`);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.sources.push(source);\n\t\treturn this;\n\t}\n\n\tappend(str, options) {\n\t\tthis.addSource({\n\t\t\tcontent: new MagicString(str),\n\t\t\tseparator: (options && options.separator) || '',\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst bundle = new Bundle({\n\t\t\tintro: this.intro,\n\t\t\tseparator: this.separator,\n\t\t});\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tbundle.addSource({\n\t\t\t\tfilename: source.filename,\n\t\t\t\tcontent: source.content.clone(),\n\t\t\t\tseparator: source.separator,\n\t\t\t});\n\t\t});\n\n\t\treturn bundle;\n\t}\n\n\tgenerateDecodedMap(options = {}) {\n\t\tconst names = [];\n\t\tlet x_google_ignoreList = undefined;\n\t\tthis.sources.forEach((source) => {\n\t\t\tObject.keys(source.content.storedNames).forEach((name) => {\n\t\t\t\tif (!~names.indexOf(name)) names.push(name);\n\t\t\t});\n\t\t});\n\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tif (i > 0) {\n\t\t\t\tmappings.advance(this.separator);\n\t\t\t}\n\n\t\t\tconst sourceIndex = source.filename ? this.uniqueSourceIndexByFilename[source.filename] : -1;\n\t\t\tconst magicString = source.content;\n\t\t\tconst locate = getLocator(magicString.original);\n\n\t\t\tif (magicString.intro) {\n\t\t\t\tmappings.advance(magicString.intro);\n\t\t\t}\n\n\t\t\tmagicString.firstChunk.eachNext((chunk) => {\n\t\t\t\tconst loc = locate(chunk.start);\n\n\t\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\t\tif (source.filename) {\n\t\t\t\t\tif (chunk.edited) {\n\t\t\t\t\t\tmappings.addEdit(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk.content,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmappings.addUneditedChunk(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk,\n\t\t\t\t\t\t\tmagicString.original,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tmagicString.sourcemapLocations,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmappings.advance(chunk.content);\n\t\t\t\t}\n\n\t\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t\t});\n\n\t\t\tif (magicString.outro) {\n\t\t\t\tmappings.advance(magicString.outro);\n\t\t\t}\n\n\t\t\tif (source.ignoreList && sourceIndex !== -1) {\n\t\t\t\tif (x_google_ignoreList === undefined) {\n\t\t\t\t\tx_google_ignoreList = [];\n\t\t\t\t}\n\t\t\t\tx_google_ignoreList.push(sourceIndex);\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : undefined,\n\t\t\tsources: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.file ? getRelativePath(options.file, source.filename) : source.filename;\n\t\t\t}),\n\t\t\tsourcesContent: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.includeContent ? source.content : null;\n\t\t\t}),\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t\tx_google_ignoreList,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\tconst indentStringCounts = {};\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tconst indentStr = source.content._getRawIndentString();\n\n\t\t\tif (indentStr === null) return;\n\n\t\t\tif (!indentStringCounts[indentStr]) indentStringCounts[indentStr] = 0;\n\t\t\tindentStringCounts[indentStr] += 1;\n\t\t});\n\n\t\treturn (\n\t\t\tObject.keys(indentStringCounts).sort((a, b) => {\n\t\t\t\treturn indentStringCounts[a] - indentStringCounts[b];\n\t\t\t})[0] || '\\t'\n\t\t);\n\t}\n\n\tindent(indentStr) {\n\t\tif (!arguments.length) {\n\t\t\tindentStr = this.getIndentString();\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\tlet trailingNewline = !this.intro || this.intro.slice(-1) === '\\n';\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\tconst indentStart = trailingNewline || (i > 0 && /\\r?\\n$/.test(separator));\n\n\t\t\tsource.content.indent(indentStr, {\n\t\t\t\texclude: source.indentExclusionRanges,\n\t\t\t\tindentStart, //: trailingNewline || /\\r?\\n$/.test( separator )  //true///\\r?\\n/.test( separator )\n\t\t\t});\n\n\t\t\ttrailingNewline = source.content.lastChar() === '\\n';\n\t\t});\n\n\t\tif (this.intro) {\n\t\t\tthis.intro =\n\t\t\t\tindentStr +\n\t\t\t\tthis.intro.replace(/^[^\\n]/gm, (match, index) => {\n\t\t\t\t\treturn index > 0 ? indentStr + match : match;\n\t\t\t\t});\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tprepend(str) {\n\t\tthis.intro = str + this.intro;\n\t\treturn this;\n\t}\n\n\ttoString() {\n\t\tconst body = this.sources\n\t\t\t.map((source, i) => {\n\t\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\t\tconst str = (i > 0 ? separator : '') + source.content.toString();\n\n\t\t\t\treturn str;\n\t\t\t})\n\t\t\t.join('');\n\n\t\treturn this.intro + body;\n\t}\n\n\tisEmpty() {\n\t\tif (this.intro.length && this.intro.trim()) return false;\n\t\tif (this.sources.some((source) => !source.content.isEmpty())) return false;\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\treturn this.sources.reduce(\n\t\t\t(length, source) => length + source.content.length(),\n\t\t\tthis.intro.length,\n\t\t);\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimStart(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\t\tthis.intro = this.intro.replace(rx, '');\n\n\t\tif (!this.intro) {\n\t\t\tlet source;\n\t\t\tlet i = 0;\n\n\t\t\tdo {\n\t\t\t\tsource = this.sources[i++];\n\t\t\t\tif (!source) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} while (!source.content.trimStartAborted(charType));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\ttrimEnd(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tlet source;\n\t\tlet i = this.sources.length - 1;\n\n\t\tdo {\n\t\t\tsource = this.sources[i--];\n\t\t\tif (!source) {\n\t\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (!source.content.trimEndAborted(charType));\n\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport Bundle from './Bundle.js';\nimport SourceMap from './SourceMap.js';\n\nMagicString.Bundle = Bundle;\nMagicString.SourceMap = SourceMap;\nMagicString.default = MagicString; // work around TypeScript bug https://github.com/Rich-Harris/magic-string/pull/121\n\nexport default MagicString;\n"], "names": ["encode"], "mappings": ";;;;AAAe,MAAM,MAAM,CAAC;AAC5B,CAAC,WAAW,CAAC,GAAG,EAAE;AAClB,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;AAC5D,EAAE;AACF;AACA,CAAC,GAAG,CAAC,CAAC,EAAE;AACR,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACrC,EAAE;AACF;AACA,CAAC,GAAG,CAAC,CAAC,EAAE;AACR,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACjD,EAAE;AACF;;ACZe,MAAM,KAAK,CAAC;AAC3B,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;AAClC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;AACpB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AAChB,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;;AAEzB,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;AACjB,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;;AAEjB,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;AACxB,EAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;AACxB,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;;AAErB,EAMS;AACT,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;AACvB,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;AACnB,GAAE;AACF,EAAC;;AAED,CAAC,UAAU,CAAC,OAAO,EAAE;AACrB,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;AACvB,EAAC;;AAED,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAA;AACnC,EAAC;;AAED,CAAC,KAAK,GAAG;AACT,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;;AAE9D,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;AAC1B,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;AAC1B,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;AAC9B,EAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;AAClC,EAAE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;;AAE5B,EAAE,OAAO,KAAK,CAAA;AACd,EAAC;;AAED,CAAC,QAAQ,CAAC,KAAK,EAAE;AACjB,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAA;AAC/C,EAAC;;AAED,CAAC,QAAQ,CAAC,EAAE,EAAE;AACd,EAAE,IAAI,KAAK,GAAG,IAAI,CAAA;AAClB,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,EAAE,CAAC,KAAK,CAAC,CAAA;AACZ,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AACrB,GAAE;AACF,EAAC;;AAED,CAAC,YAAY,CAAC,EAAE,EAAE;AAClB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAA;AAClB,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,EAAE,CAAC,KAAK,CAAC,CAAA;AACZ,GAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAA;AACzB,GAAE;AACF,EAAC;;AAED,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;AACvC,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;AACxB,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;AAClB,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;AAClB,GAAE;AACF,EAAE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;;AAE5B,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;;AAEpB,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;AACnC,EAAC;;AAED,CAAC,YAAY,CAAC,OAAO,EAAE;AACvB,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;AACnC,EAAC;;AAED,CAAC,KAAK,GAAG;AACT,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;AACjB,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;AACjB,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AACnB,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAA;AAC/B,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;AACzB,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;AACtB,GAAE;AACF,EAAC;;AAED,CAAC,KAAK,CAAC,KAAK,EAAE;AACd,EAAE,MAAM,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;;AAEvC,EAAE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;AAC3D,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;;AAEvD,EAAE,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAA;;AAEhC,EAAE,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;AAC5D,EAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;AAC7B,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;;AAEjB,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAA;;AAElB,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;AAC3B,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;AACpB,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,OAAO,GAAG,cAAc,CAAA;AAChC,GAAE;;AAEF,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;AAC3B,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;AACtD,EAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAA;AAC1B,EAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;;AAEtB,EAAE,OAAO,QAAQ,CAAA;AACjB,EAAC;;AAED,CAAC,QAAQ,GAAG;AACZ,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;AAC/C,EAAC;;AAED,CAAC,OAAO,CAAC,EAAE,EAAE;AACb,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;AAEpC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;;AAE9C,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,GAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;AACjC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;AACrE,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB;AACA,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAC7C,KAAI;AACJ,IAAG;AACH,GAAG,OAAO,IAAI,CAAA;AACd,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;;AAEjC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AAC1C,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;AACrC,GAAE;AACF,EAAC;;AAED,CAAC,SAAS,CAAC,EAAE,EAAE;AACf,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;AAEpC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;;AAE9C,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,GAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;AACjC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;AAC1D,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB;AACA,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AACjD,KAAI;AACJ,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;AAClC,IAAG;AACH,GAAG,OAAO,IAAI,CAAA;AACd,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;;AAEjC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AAC1C,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;AACrC,GAAE;AACF,EAAC;AACD;;ACrLA,SAAS,OAAO,GAAG;AACnB,CAAC,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,UAAU,EAAE;AACjF,EAAE,OAAO,CAAC,GAAG,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,EAAE,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC1C,EAAE,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/D,EAAE,MAAM;AACR,EAAE,OAAO,MAAM;AACf,GAAG,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;AAC9F,GAAG,CAAC;AACJ,EAAE;AACF,CAAC;AACD;AACA,MAAM,IAAI,iBAAiB,OAAO,EAAE,CAAC;AACrC;AACe,MAAM,SAAS,CAAC;AAC/B,CAAC,WAAW,CAAC,UAAU,EAAE;AACzB,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACnB,EAAE,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;AAC9B,EAAE,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACpC,EAAE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;AAClD,EAAE,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;AAChC,EAAE,IAAI,CAAC,QAAQ,GAAGA,qBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,UAAU,CAAC,mBAAmB,KAAK,WAAW,EAAE;AAC7D,GAAG,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;AAC7D,GAAG;AACH,EAAE,IAAI,OAAO,UAAU,CAAC,OAAO,KAAK,WAAW,EAAE;AACjD,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACrC,GAAG;AACH,EAAE;AACF;AACA,CAAC,QAAQ,GAAG;AACZ,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9B,EAAE;AACF;AACA,CAAC,KAAK,GAAG;AACT,EAAE,OAAO,6CAA6C,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC/E,EAAE;AACF;;ACvCe,SAAS,WAAW,CAAC,IAAI,EAAE;AAC1C,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,CAAC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1D,CAAC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D;AACA,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AACjD,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA;AACA;AACA;AACA,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;AACrC,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA;AACA,CAAC,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;AAClD,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACvC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACd;AACA,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrC;;ACxBe,SAAS,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;AAClD,CAAC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvC,CAAC,MAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACnC;AACA,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AACjB;AACA,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;AACrC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;AACpB,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;AAClB,EAAE;AACF;AACA,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE;AACvB,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AAC3B,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAClC,EAAE;AACF;AACA,CAAC,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5C;;ACjBA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC3C;AACe,SAAS,QAAQ,CAAC,KAAK,EAAE;AACxC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC;AACnD;;ACJe,SAAS,UAAU,CAAC,MAAM,EAAE;AAC3C,CAAC,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1C,CAAC,MAAM,WAAW,GAAG,EAAE,CAAC;AACxB;AACA,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzD,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrC,EAAE;AACF;AACA,CAAC,OAAO,SAAS,MAAM,CAAC,KAAK,EAAE;AAC/B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;AAC7B,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;AAChB,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,GAAG,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;AAC/B,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAI,MAAM;AACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACd,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,EAAE,MAAM,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1B,EAAE,CAAC;AACH;;ACxBA,MAAM,SAAS,GAAG,IAAI,CAAC;AACvB;AACe,MAAM,QAAQ,CAAC;AAC9B,CAAC,WAAW,CAAC,KAAK,EAAE;AACpB,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAChB,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC3D,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACtB,EAAE;AACF;AACA,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;AAC/C,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,GAAG,MAAM,qBAAqB,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACpD,GAAG,IAAI,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACjD,GAAG,IAAI,sBAAsB,GAAG,CAAC,CAAC,CAAC;AACnC;AACA;AACA,GAAG,OAAO,cAAc,IAAI,CAAC,IAAI,qBAAqB,GAAG,cAAc,EAAE;AACzE,IAAI,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AAClF,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE;AACxB,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC7D,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AACjC;AACA,IAAI,sBAAsB,GAAG,cAAc,CAAC;AAC5C,IAAI,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;AAC/D,IAAI;AACJ;AACA,GAAG,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AACjF,GAAG,IAAI,SAAS,IAAI,CAAC,EAAE;AACvB,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5B,IAAI;AACJ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClC;AACA,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACtB,EAAE;AACF;AACA,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE;AACzE,EAAE,IAAI,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC;AACtC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB;AACA,EAAE,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAClC;AACA,EAAE,OAAO,iBAAiB,GAAG,KAAK,CAAC,GAAG,EAAE;AACxC,GAAG,IAAI,QAAQ,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;AAC7C,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;AAClB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC7D,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AACjC,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAChC,IAAI,MAAM;AACV,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;AAC1E,KAAK,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AACnF;AACA,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,EAAE;AACpC;AACA,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;AACvD;AACA,OAAO,IAAI,CAAC,mBAAmB,EAAE;AACjC,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvC,QAAQ,mBAAmB,GAAG,IAAI,CAAC;AACnC,QAAQ;AACR,OAAO,MAAM;AACb;AACA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtC,OAAO,mBAAmB,GAAG,KAAK,CAAC;AACnC,OAAO;AACP,MAAM,MAAM;AACZ,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrC,MAAM;AACN,KAAK;AACL;AACA,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;AAClC,IAAI,KAAK,GAAG,KAAK,CAAC;AAClB,IAAI;AACJ;AACA,GAAG,iBAAiB,IAAI,CAAC,CAAC;AAC1B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACtB,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,EAAE;AACd,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO;AACnB;AACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC7D,IAAI;AACJ,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AAChC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;AAC7D,EAAE;AACF;;ACtGA,MAAM,CAAC,GAAG,IAAI,CAAA;;AAEd,MAAM,MAAM,GAAG;AACf,CAAC,UAAU,EAAE,KAAK;AAClB,CAAC,WAAW,EAAE,KAAK;AACnB,CAAC,SAAS,EAAE,KAAK;AACjB,CAAC,CAAA;;AAEc,MAAM,WAAW,CAAC;AACjC,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;AACnC,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;;AAEnD,EAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;AAChC,GAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;AAC9C,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACvC,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACvC,GAAG,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAC/C,GAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAC9C,GAAG,iBAAiB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AACtD,GAAG,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACzC,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACvC,GAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE;AACxD,GAAG,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,qBAAqB,EAAE;AAClF,GAAG,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;AAC9D,GAAG,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AAC7C,GAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;AAClD,GAAG,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,UAAU,EAAE;AAC5D,GAAG,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;AACzD,GAAG,CAAC,CAAA;;AAMJ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAA;AACzB,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAA;AACnC,EAAC;;AAED,CAAC,oBAAoB,CAAC,IAAI,EAAE;AAC5B,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;AACnC,EAAC;;AAED,CAAC,MAAM,CAAC,OAAO,EAAE;AACjB,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAA;;AAExF,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;AACvB,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE;AAC5B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;AAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;AAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;AAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;AAEjC,EAAE,IAAI,KAAK,EAAE;AACb,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;AAC5B,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;AACxB,GAAE;AAGF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;AAC7B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;AAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;AAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;AAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;AAEnC,EAAE,IAAI,KAAK,EAAE;AACb,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;AAC7B,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;AACxB,GAAE;AAGF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,KAAK,GAAG;AACT,EAAE,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;;AAEjG,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAA;AACrC,EAAE,IAAI,WAAW,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAA;;AAE1F,EAAE,OAAO,aAAa,EAAE;AACxB,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAA;AAClD,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAA;;AAE9C,GAAG,MAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAA;AAC/C,GAAG,MAAM,eAAe,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,EAAE,CAAA;;AAEzE,GAAG,IAAI,eAAe,EAAE;AACxB,IAAI,WAAW,CAAC,IAAI,GAAG,eAAe,CAAA;AACtC,IAAI,eAAe,CAAC,QAAQ,GAAG,WAAW,CAAA;;AAE1C,IAAI,WAAW,GAAG,eAAe,CAAA;AACjC,IAAG;;AAEH,GAAG,aAAa,GAAG,iBAAiB,CAAA;AACpC,GAAE;;AAEF,EAAE,MAAM,CAAC,SAAS,GAAG,WAAW,CAAA;;AAEhC,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAClC,GAAG,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAA;AACpE,GAAE;;AAEF,EAAE,MAAM,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;;AAEjE,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;AAC3B,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;;AAE3B,EAAE,OAAO,MAAM,CAAA;AACf,EAAC;;AAED,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC7B,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;;AAEzB,EAAE,MAAM,WAAW,GAAG,CAAC,CAAA;AACvB,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAC7C,EAAE,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;AAE9C,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;;AAE1C,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAC/B,GAAE;;AAEF,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,KAAK;AACtC,GAAG,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;AAElC,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;AAExD,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE;AACrB,IAAI,QAAQ,CAAC,OAAO;AACpB,KAAK,WAAW;AAChB,KAAK,KAAK,CAAC,OAAO;AAClB,KAAK,GAAG;AACR,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzD,KAAK,CAAA;AACL,IAAI,MAAM;AACV,IAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAA;AAC9F,IAAG;;AAEH,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;AACxD,GAAG,CAAC,CAAA;;AAEJ,EAAE,OAAO;AACT,GAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS;AACrE,GAAG,OAAO,EAAE;AACZ,IAAI,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE;AAC7F,IAAI;AACJ,GAAG,cAAc,EAAE,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS;AACvE,GAAG,KAAK;AACR,GAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;AACzB,GAAG,mBAAmB,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,WAAW,CAAC,GAAG,SAAS;AACnE,GAAG,CAAA;AACH,EAAC;;AAED,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAA;AACxD,EAAC;;AAED,CAAC,gBAAgB,GAAG;AACpB,EAAE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;AACpC,GAAG,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AAC9C,GAAE;AACF,EAAC;;AAED,CAAC,mBAAmB,GAAG;AACvB,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAA;AACzB,EAAE,OAAO,IAAI,CAAC,SAAS,CAAA;AACvB,EAAC;;AAED,CAAC,eAAe,GAAG;AACnB,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAA;AACzB,EAAE,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAA;AACxD,EAAC;;AAED,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5B,EAAE,MAAM,OAAO,GAAG,YAAY,CAAA;;AAE9B,EAAE,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC3B,GAAG,OAAO,GAAG,SAAS,CAAA;AACtB,GAAG,SAAS,GAAG,SAAS,CAAA;AACxB,GAAE;;AAEF,EAAE,IAAI,SAAS,KAAK,SAAS,EAAE;AAC/B,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAA;AAC1B,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAA;AACrC,GAAE;;AAEF,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC;;AAEpC,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;;AAEzB;AACA,EAAE,MAAM,UAAU,GAAG,EAAE,CAAA;;AAEvB,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;AACvB,GAAG,MAAM,UAAU;AACnB,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAA;AAChF,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACzD,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;AACzB,KAAI;AACJ,IAAI,CAAC,CAAA;AACL,GAAE;;AAEF,EAAE,IAAI,yBAAyB,GAAG,OAAO,CAAC,WAAW,KAAK,KAAK,CAAA;AAC/D,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,KAAK;AAC9B,GAAG,IAAI,yBAAyB,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;AAC/D,GAAG,yBAAyB,GAAG,IAAI,CAAA;AACnC,GAAG,OAAO,KAAK,CAAA;AACf,GAAG,CAAA;;AAEH,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;;AAEpD,EAAE,IAAI,SAAS,GAAG,CAAC,CAAA;AACnB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;;AAE7B,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;;AAExB,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AAChC,KAAK,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;;AAE7D,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;AAC/B,MAAM,yBAAyB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAA;AAClF,MAAK;AACL,KAAI;AACJ,IAAI,MAAM;AACV,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAA;;AAE3B,IAAI,OAAO,SAAS,GAAG,GAAG,EAAE;AAC5B,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AACjC,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;;AAE3C,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AACzB,OAAO,yBAAyB,GAAG,IAAI,CAAA;AACvC,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,yBAAyB,EAAE;AAC7D,OAAO,yBAAyB,GAAG,KAAK,CAAA;;AAExC,OAAO,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE;AACtC,QAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;AACrC,QAAQ,MAAM;AACd,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;AAC1C,QAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AAC1B,QAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;AACrC,QAAO;AACP,OAAM;AACN,MAAK;;AAEL,KAAK,SAAS,IAAI,CAAC,CAAA;AACnB,KAAI;AACJ,IAAG;;AAEH,GAAG,SAAS,GAAG,KAAK,CAAC,GAAG,CAAA;AACxB,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AACrB,GAAE;;AAEF,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;;AAEpD,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,MAAM,GAAG;AACV,EAAE,MAAM,IAAI,KAAK;AACjB,GAAG,iFAAiF;AACpF,GAAG,CAAA;AACH,EAAC;;AAED,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE;AAC5B,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AAC1B,GAAG,OAAO,CAAC,IAAI;AACf,IAAI,oFAAoF;AACxF,IAAI,CAAA;AACJ,GAAG,MAAM,CAAC,UAAU,GAAG,IAAI,CAAA;AAC3B,GAAE;;AAEF,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;AACxC,EAAC;;AAED,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;AAC7B,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;AAC3B,GAAG,OAAO,CAAC,IAAI;AACf,IAAI,uFAAuF;AAC3F,IAAI,CAAA;AACJ,GAAG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAA;AAC5B,GAAE;;AAEF,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;AAC1C,EAAC;;AAED,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACzB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;AAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;AACzB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;AAE7B,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;;AAI9F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AAClB,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;AAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;AACnC,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;;AAE9B,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAA;AAChC,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAA;;AAE5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;AACtC,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,CAAA;AACvD,EAAE,MAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAA;;AAE/D,EAAE,IAAI,OAAO,EAAE,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAA;AACtC,EAAE,IAAI,QAAQ,EAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAA;;AAE3C,EAAE,IAAI,OAAO,EAAE,OAAO,CAAC,IAAI,GAAG,KAAK,CAAA;AACnC,EAAE,IAAI,QAAQ,EAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAA;;AAExC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAA;AAClD,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AAClB,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAA;AAClC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAA;AAC7B,GAAE;;AAEF,EAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAA;AAC1B,EAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAA;;AAE9B,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;AACvC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;AAGtC,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;AACzC,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;AACzB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAA;AAC1F,EAAC;;AAED,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;AACtC,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;AAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;AAEzB,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAA;;AAE9F,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAC9C,GAAE;;AAEF,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;AACzE,EAAE,IAAI,KAAK,KAAK,GAAG;AACnB,GAAG,MAAM,IAAI,KAAK;AAClB,IAAI,+EAA+E;AACnF,IAAI,CAAA;;AAIJ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;;AAElB,EAAE,IAAI,OAAO,KAAK,IAAI,EAAE;AACxB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAC1B,IAAI,OAAO,CAAC,IAAI;AAChB,KAAK,+HAA+H;AACpI,KAAK,CAAA;AACL,IAAI,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;AAC3B,IAAG;;AAEH,GAAG,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAA;AAChC,GAAE;AACF,EAAE,MAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAA;AACrE,EAAE,MAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAA;;AAErE,EAAE,IAAI,SAAS,EAAE;AACjB,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AACnD,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE;AACrD,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,KAAK,EAAE,IAAI;AACf,IAAI,UAAU,EAAE,IAAI;AACpB,IAAI,CAAC,CAAA;AACL,GAAE;;AAEF,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;AACnC,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;;AAE9B,EAAE,IAAI,KAAK,EAAE;AACb,GAAG,IAAI,KAAK,GAAG,KAAK,CAAA;AACpB,GAAG,OAAO,KAAK,KAAK,IAAI,EAAE;AAC1B,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAChD,KAAK,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;AAC7D,KAAI;AACJ,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AACtB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;AACzB,IAAG;;AAEH,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC,CAAA;AAC7C,GAAG,MAAM;AACT;AACA,GAAG,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;;AAEtE;AACA,GAAG,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;AACvB,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAA;AAC3B,GAAE;AAGF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,OAAO,CAAC,OAAO,EAAE;AAClB,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAA;;AAExF,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;AACnC,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;AAC7B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;AAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;AAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;AAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;AAEjC,EAAE,IAAI,KAAK,EAAE;AACb,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;AAC7B,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;AACpC,GAAE;AAGF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;AAC9B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;AAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;AAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;AAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;AAEnC,EAAE,IAAI,KAAK,EAAE;AACb,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;AAC9B,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;AACpC,GAAE;AAGF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE;AACpB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;AAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;AAEzB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAC9C,GAAE;;AAEF,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,OAAO,IAAI,CAAA;;AAEhC,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;AAC5F,EAAE,IAAI,KAAK,GAAG,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;;AAIpE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;;AAElB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;AAEjC,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;AACnB,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;AACnB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;;AAEjB,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;AAC3D,GAAE;AAGF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE;AACnB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;AAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;AAEzB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAC9C,GAAE;;AAEF,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,OAAO,IAAI,CAAA;;AAEhC,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;AAC5F,EAAE,IAAI,KAAK,GAAG,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;;AAIpE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;;AAElB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;AAEjC,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;;AAEhB,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;AAC3D,GAAE;AAGF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,QAAQ,GAAG;AACZ,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;AACjE,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;AAC5B,EAAE,GAAG;AACL,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;AACrE,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;AAC3E,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;AACrE,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAE;AACpC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;AACjE,EAAE,OAAO,EAAE,CAAA;AACX,EAAC;;AAED,CAAC,QAAQ,GAAG;AACZ,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;AAC3C,EAAE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;AAC/D,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;AAC1B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;AAC5B,EAAE,GAAG;AACL,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;AAC1C,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;AAC5E,IAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAA;AACnC,IAAG;;AAEH,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,IAAI,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;AAC5C,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;AAC9E,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAA;AACrC,IAAG;;AAEH,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;AAC1C,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;AAC5E,IAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAA;AACnC,IAAG;AACH,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAE;AACpC,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;AACvC,EAAE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;AACzE,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,CAAA;AAC7B,EAAC;;AAED,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AAC5D,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;AAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;AAEzB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;AAC9C,GAAE;;AAEF,EAAE,IAAI,MAAM,GAAG,EAAE,CAAA;;AAEjB;AACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;AAC7B,EAAE,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;AAC/D;AACA,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE;AAC9C,IAAI,OAAO,MAAM,CAAA;AACjB,IAAG;;AAEH,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AACrB,GAAE;;AAEF,EAAE,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;AACpD,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAA;;AAEnF,EAAE,MAAM,UAAU,GAAG,KAAK,CAAA;AAC1B,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;AACvE,IAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAA;AACzB,IAAG;;AAEH,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAA;AAC5D,GAAG,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;AACvD,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAA;;AAEhF,GAAG,MAAM,UAAU,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA;AACpE,GAAG,MAAM,QAAQ,GAAG,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAA;;AAE/F,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;;AAEtD,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;AAC3D,IAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAA;AACzB,IAAG;;AAEH,GAAG,IAAI,WAAW,EAAE;AACpB,IAAI,MAAK;AACT,IAAG;;AAEH,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AACrB,GAAE;;AAEF,EAAE,OAAO,MAAM,CAAA;AACf,EAAC;;AAED;AACA,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;AAClB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;AAC5B,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACxB,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;;AAE1C,EAAE,OAAO,KAAK,CAAA;AACd,EAAC;;AAED,CAAC,MAAM,CAAC,KAAK,EAAE;AACf,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,OAAM;;AAItD,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAA;AACpC,EAAE,MAAM,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAA;;AAEzC,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;;AAEnE,GAAG,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;AAC5E,GAAE;AACF,EAAC;;AAED,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;AAC3B,EAAE,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;AAC5C;AACA,GAAG,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAA;AAC/C,GAAG,MAAM,IAAI,KAAK;AAClB,IAAI,CAAC,mDAAmD,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;AACzG,IAAI,CAAA;AACJ,GAAE;;AAEF,EAAE,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;AAErC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;AAC3B,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;AAChC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;;AAErC,EAAE,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;;AAEzD,EAAE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;AAEhC,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,QAAQ,GAAG;AACZ,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;;AAEtB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;AAC7B,EAAE,OAAO,KAAK,EAAE;AAChB,GAAG,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAA;AAC1B,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AACrB,GAAE;;AAEF,EAAE,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;AACzB,EAAC;;AAED,CAAC,OAAO,GAAG;AACX,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;AAC7B,EAAE,GAAG;AACL,GAAG;AACH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;AAC7C,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAClD,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;AAC7C;AACA,IAAI,OAAO,KAAK,CAAA;AAChB,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAE;AAChC,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,MAAM,GAAG;AACV,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;AAC7B,EAAE,IAAI,MAAM,GAAG,CAAC,CAAA;AAChB,EAAE,GAAG;AACL,GAAG,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAA;AAC3E,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAE;AAChC,EAAE,OAAO,MAAM,CAAA;AACf,EAAC;;AAED,CAAC,SAAS,GAAG;AACb,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;AAC9B,EAAC;;AAED,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;AACnD,EAAC;;AAED,CAAC,cAAc,CAAC,QAAQ,EAAE;AAC1B,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAA;;AAEnD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;AAEpC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;;AAE5B,EAAE,GAAG;AACL,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;AACxB,GAAG,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;;AAEpC;AACA,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1B,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAClC,KAAK,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAA;AAChC,KAAI;;AAEJ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;AACjC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;AAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;AAC3C,IAAG;;AAEH,GAAG,IAAI,OAAO,EAAE,OAAO,IAAI,CAAA;AAC3B,GAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAA;AACzB,GAAG,QAAQ,KAAK,EAAC;;AAEjB,EAAE,OAAO,KAAK,CAAA;AACd,EAAC;;AAED,CAAC,OAAO,CAAC,QAAQ,EAAE;AACnB,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAC/B,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;AACD,CAAC,gBAAgB,CAAC,QAAQ,EAAE;AAC5B,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAA;;AAExD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;AAEpC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;;AAE7B,EAAE,GAAG;AACL,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;AACxB,GAAG,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;;AAEtC,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1B;AACA,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAA;;AAE7D,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;AACjC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;AAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;AAC3C,IAAG;;AAEH,GAAG,IAAI,OAAO,EAAE,OAAO,IAAI,CAAA;AAC3B,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;AACrB,GAAG,QAAQ,KAAK,EAAC;;AAEjB,EAAE,OAAO,KAAK,CAAA;AACd,EAAC;;AAED,CAAC,SAAS,CAAC,QAAQ,EAAE;AACrB,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;AACjC,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,UAAU,GAAG;AACd,EAAE,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAA;AAC1C,EAAC;;AAED,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE;AAC1C,EAAE,SAAS,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE;AACtC,GAAG,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACxC,IAAI,OAAO,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AAC1D;AACA,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,OAAO,GAAG,CAAA;AAC9B,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAA;AACnC,KAAK,MAAM,GAAG,GAAG,CAAC,CAAC,CAAA;AACnB,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC7C,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AACnB,KAAK,CAAC,CAAA;AACN,IAAI,MAAM;AACV,IAAI,OAAO,WAAW,CAAC,GAAG,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;AAChE,IAAG;AACH,GAAE;AACF,EAAE,SAAS,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE;AAC7B,GAAG,IAAI,KAAK,CAAA;AACZ,GAAG,MAAM,OAAO,GAAG,EAAE,CAAA;AACrB,GAAG,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;AAClC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AACvB,IAAG;AACH,GAAG,OAAO,OAAO,CAAA;AACjB,GAAE;AACF,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;AAC1B,GAAG,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;AACvD,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC9B,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;AAC7B,KAAK,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;AAC7D,KAAK,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;AACnC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;AAC7E,MAAK;AACL,KAAI;AACJ,IAAI,CAAC,CAAA;AACL,GAAG,MAAM;AACT,GAAG,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;AACjD,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;AACrC,IAAI,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;AAC5D,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;AAClC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;AAC5E,KAAI;AACJ,IAAG;AACH,GAAE;AACF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE;AACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAA;AAC3B,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;;AAExC,EAAE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AACpB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;AAC5D,GAAE;;AAEF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE;AACnC,EAAE,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACvC,GAAG,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;AACvD,GAAE;;AAEF,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;AACtD,EAAC;;AAED,CAAC,iBAAiB,CAAC,MAAM,EAAE,WAAW,EAAE;AACxC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAA;AAC3B,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAA;AACpC,EAAE;AACF,GAAG,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,GAAG,KAAK,KAAK,CAAC,CAAC;AACf,GAAG,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,YAAY,CAAA;AACxD,IAAI;AACJ,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,YAAY,CAAC,CAAA;AAC/D,GAAG,IAAI,QAAQ,KAAK,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,YAAY,EAAE,WAAW,CAAC,CAAA;AACzF,GAAE;;AAEF,EAAE,OAAO,IAAI,CAAA;AACb,EAAC;;AAED,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE;AACtC,EAAE,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACvC,GAAG,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;AAC1D,GAAE;;AAEF,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AAC3B,GAAG,MAAM,IAAI,SAAS;AACtB,IAAI,2EAA2E;AAC/E,IAAI,CAAA;AACJ,GAAE;;AAEF,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;AACtD,EAAC;AACD;;AC33BA,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AACnD;AACe,MAAM,MAAM,CAAC;AAC5B,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC3B,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;AACnC,EAAE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9E,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACpB,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC1B,EAAE,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;AACxC,EAAE;AACF;AACA,CAAC,SAAS,CAAC,MAAM,EAAE;AACnB,EAAE,IAAI,MAAM,YAAY,WAAW,EAAE;AACrC,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC;AACzB,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC7B,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,IAAI,CAAC,CAAC;AACN,GAAG;AACH;AACA,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC5C,GAAG,MAAM,IAAI,KAAK;AAClB,IAAI,sIAAsI;AAC1I,IAAI,CAAC;AACL,GAAG;AACH;AACA,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACvF,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjF,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;AACtC;AACA,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;AAC5E,IAAI,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;AAClF,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC7F,IAAI,MAAM;AACV,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/F,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,YAAY,CAAC,OAAO,EAAE;AAC1D,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC,+BAA+B,EAAE,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC;AAC/F,KAAK;AACL,IAAI;AACJ,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5B,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE;AACtB,EAAE,IAAI,CAAC,SAAS,CAAC;AACjB,GAAG,OAAO,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC;AAChC,GAAG,SAAS,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE;AAClD,GAAG,CAAC,CAAC;AACL;AACA,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,KAAK,GAAG;AACT,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC;AAC5B,GAAG,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,GAAG,SAAS,EAAE,IAAI,CAAC,SAAS;AAC5B,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACnC,GAAG,MAAM,CAAC,SAAS,CAAC;AACpB,IAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC7B,IAAI,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AACnC,IAAI,SAAS,EAAE,MAAM,CAAC,SAAS;AAC/B,IAAI,CAAC,CAAC;AACN,GAAG,CAAC,CAAC;AACL;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACF;AACA,CAAC,kBAAkB,CAAC,OAAO,GAAG,EAAE,EAAE;AAClC,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB,EAAE,IAAI,mBAAmB,GAAG,SAAS,CAAC;AACtC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACnC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC7D,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChD,IAAI,CAAC,CAAC;AACN,GAAG,CAAC,CAAC;AACL;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;AACA,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;AACtC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;AACd,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,IAAI;AACJ;AACA,GAAG,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChG,GAAG,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;AACtC,GAAG,MAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnD;AACA,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;AAC1B,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxC,IAAI;AACJ;AACA,GAAG,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,KAAK;AAC9C,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC;AACA,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACzB,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AACvB,MAAM,QAAQ,CAAC,OAAO;AACtB,OAAO,WAAW;AAClB,OAAO,KAAK,CAAC,OAAO;AACpB,OAAO,GAAG;AACV,OAAO,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC3D,OAAO,CAAC;AACR,MAAM,MAAM;AACZ,MAAM,QAAQ,CAAC,gBAAgB;AAC/B,OAAO,WAAW;AAClB,OAAO,KAAK;AACZ,OAAO,WAAW,CAAC,QAAQ;AAC3B,OAAO,GAAG;AACV,OAAO,WAAW,CAAC,kBAAkB;AACrC,OAAO,CAAC;AACR,MAAM;AACN,KAAK,MAAM;AACX,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACrC,KAAK;AACL;AACA,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1D,IAAI,CAAC,CAAC;AACN;AACA,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;AAC1B,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxC,IAAI;AACJ;AACA,GAAG,IAAI,MAAM,CAAC,UAAU,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AAChD,IAAI,IAAI,mBAAmB,KAAK,SAAS,EAAE;AAC3C,KAAK,mBAAmB,GAAG,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC1C,IAAI;AACJ,GAAG,CAAC,CAAC;AACL;AACA,EAAE,OAAO;AACT,GAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS;AACrE,GAAG,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AAC/C,IAAI,OAAO,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC3F,IAAI,CAAC;AACL,GAAG,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AACtD,IAAI,OAAO,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1D,IAAI,CAAC;AACL,GAAG,KAAK;AACR,GAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;AACzB,GAAG,mBAAmB;AACtB,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;AACzD,EAAE;AACF;AACA,CAAC,eAAe,GAAG;AACnB,EAAE,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAChC;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACnC,GAAG,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;AAC1D;AACA,GAAG,IAAI,SAAS,KAAK,IAAI,EAAE,OAAO;AAClC;AACA,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACzE,GAAG,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACtC,GAAG,CAAC,CAAC;AACL;AACA,EAAE;AACF,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAClD,IAAI,OAAO,kBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;AACzD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAChB,IAAI;AACJ,EAAE;AACF;AACA,CAAC,MAAM,CAAC,SAAS,EAAE;AACnB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,GAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AACtC,GAAG;AACH;AACA,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC;AACpC;AACA,EAAE,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACrE;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;AACtC,GAAG,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACxF,GAAG,MAAM,WAAW,GAAG,eAAe,KAAK,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9E;AACA,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;AACpC,IAAI,OAAO,EAAE,MAAM,CAAC,qBAAqB;AACzC,IAAI,WAAW;AACf,IAAI,CAAC,CAAC;AACN;AACA,GAAG,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;AACxD,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,GAAG,IAAI,CAAC,KAAK;AACb,IAAI,SAAS;AACb,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK;AACrD,KAAK,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;AAClD,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,EAAE;AACd,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;AAChC,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,QAAQ,GAAG;AACZ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO;AAC3B,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;AACvB,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACzF,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrE;AACA,IAAI,OAAO,GAAG,CAAC;AACf,IAAI,CAAC;AACL,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACb;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,EAAE;AACF;AACA,CAAC,OAAO,GAAG;AACX,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC;AAC3D,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,KAAK,CAAC;AAC7E,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,MAAM,GAAG;AACV,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;AAC5B,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE;AACvD,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AACpB,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,SAAS,GAAG;AACb,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/B,EAAE;AACF;AACA,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpD,EAAE;AACF;AACA,CAAC,SAAS,CAAC,QAAQ,EAAE;AACrB,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACzD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACnB,GAAG,IAAI,MAAM,CAAC;AACd,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACb;AACA,GAAG,GAAG;AACN,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,KAAK,MAAM;AACX,KAAK;AACL,IAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AACxD,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,OAAO,CAAC,QAAQ,EAAE;AACnB,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACpD;AACA,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAClC;AACA,EAAE,GAAG;AACL,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9B,GAAG,IAAI,CAAC,MAAM,EAAE;AAChB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC5C,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;AACrD;AACA,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;;ACpSA,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5B,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;AAClC,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC;;;;"}