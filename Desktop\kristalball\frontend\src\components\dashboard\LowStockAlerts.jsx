import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

const LowStockAlerts = ({ alerts }) => {
  if (!alerts || alerts.length === 0) {
    return (
      <div className="card p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Low Stock Alerts</h3>
        <div className="text-center py-8">
          <div className="text-green-600 mb-2">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-sm text-gray-500">All assets are adequately stocked!</p>
        </div>
      </div>
    );
  }

  const getStockLevel = (current, minimum) => {
    const ratio = current / minimum;
    if (ratio <= 0.5) return { level: 'critical', color: 'text-red-600', bgColor: 'bg-red-100' };
    if (ratio <= 1) return { level: 'low', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { level: 'adequate', color: 'text-green-600', bgColor: 'bg-green-100' };
  };

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Low Stock Alerts</h3>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          {alerts.length} Alert{alerts.length !== 1 ? 's' : ''}
        </span>
      </div>
      
      <div className="space-y-3">
        {alerts.map((item) => {
          const stockInfo = getStockLevel(item.currentStock, item.asset?.minimumStock || 0);
          
          return (
            <div
              key={item._id}
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <div className="flex items-center space-x-3">
                <div className={`flex-shrink-0 ${stockInfo.bgColor} rounded-full p-2`}>
                  <ExclamationTriangleIcon className={`h-4 w-4 ${stockInfo.color}`} />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {item.asset?.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {item.asset?.type} • {item.base?.name}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs text-gray-600">
                      Current: {item.currentStock}
                    </span>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs text-gray-600">
                      Minimum: {item.asset?.minimumStock}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex-shrink-0">
                <div className="text-right">
                  <div className={`text-sm font-medium ${stockInfo.color}`}>
                    {stockInfo.level === 'critical' ? 'Critical' : 'Low Stock'}
                  </div>
                  <div className="text-xs text-gray-500">
                    {Math.round((item.currentStock / (item.asset?.minimumStock || 1)) * 100)}% of minimum
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {alerts.length > 5 && (
        <div className="mt-4 text-center">
          <button className="text-sm text-military-600 hover:text-military-800 font-medium">
            View All Alerts ({alerts.length})
          </button>
        </div>
      )}
    </div>
  );
};

export default LowStockAlerts;
