import { 
  ShoppingCartIcon, 
  ArrowsRightLeftIcon, 
  UserGroupIcon 
} from '@heroicons/react/24/outline';

const RecentActivities = ({ activities }) => {
  const getActivityIcon = (type) => {
    switch (type) {
      case 'purchase':
        return ShoppingCartIcon;
      case 'transfer':
        return ArrowsRightLeftIcon;
      case 'assignment':
        return UserGroupIcon;
      default:
        return ShoppingCartIcon;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'purchase':
        return 'bg-green-500';
      case 'transfer':
        return 'bg-blue-500';
      case 'assignment':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatActivity = (activity, type) => {
    switch (type) {
      case 'purchase':
        return {
          title: `Purchase Order ${activity.purchaseOrderNumber}`,
          description: `${activity.quantity} ${activity.asset?.name} - $${activity.totalCost?.toLocaleString()}`,
          date: new Date(activity.purchaseDate).toLocaleDateString(),
          status: activity.status
        };
      case 'transfer':
        return {
          title: `Transfer ${activity.transferNumber}`,
          description: `${activity.quantity} ${activity.asset?.name} from ${activity.fromBase?.name} to ${activity.toBase?.name}`,
          date: new Date(activity.requestDate).toLocaleDateString(),
          status: activity.status
        };
      case 'assignment':
        return {
          title: `Assignment ${activity.assignmentNumber}`,
          description: `${activity.quantity} ${activity.asset?.name} to ${activity.assignedTo?.name}`,
          date: new Date(activity.assignmentDate).toLocaleDateString(),
          status: activity.status
        };
      default:
        return {
          title: 'Unknown Activity',
          description: '',
          date: '',
          status: ''
        };
    }
  };

  const allActivities = [
    ...(activities?.purchases || []).map(p => ({ ...p, type: 'purchase' })),
    ...(activities?.transfers || []).map(t => ({ ...t, type: 'transfer' })),
    ...(activities?.assignments || []).map(a => ({ ...a, type: 'assignment' }))
  ].sort((a, b) => new Date(b.createdAt || b.purchaseDate || b.requestDate || b.assignmentDate) - 
                   new Date(a.createdAt || a.purchaseDate || a.requestDate || a.assignmentDate));

  return (
    <div className="card p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activities</h3>
      
      {allActivities.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No recent activities found.
        </div>
      ) : (
        <div className="flow-root">
          <ul className="-mb-8">
            {allActivities.slice(0, 10).map((activity, index) => {
              const Icon = getActivityIcon(activity.type);
              const formatted = formatActivity(activity, activity.type);
              
              return (
                <li key={`${activity.type}-${activity._id}`}>
                  <div className="relative pb-8">
                    {index !== allActivities.slice(0, 10).length - 1 && (
                      <span
                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                        aria-hidden="true"
                      />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className={`${getActivityColor(activity.type)} h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white`}>
                          <Icon className="h-4 w-4 text-white" />
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {formatted.title}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatted.description}
                          </p>
                          <div className="mt-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              formatted.status === 'delivered' || formatted.status === 'received' || formatted.status === 'assigned'
                                ? 'bg-green-100 text-green-800'
                                : formatted.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : formatted.status === 'approved'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {formatted.status?.charAt(0).toUpperCase() + formatted.status?.slice(1)}
                            </span>
                          </div>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500">
                          {formatted.date}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};

export default RecentActivities;
