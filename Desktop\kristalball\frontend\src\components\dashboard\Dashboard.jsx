import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { dashboardAPI } from '../../services/api';
import MetricsCards from './MetricsCards';
import FiltersPanel from './FiltersPanel';
import RecentActivities from './RecentActivities';
import LowStockAlerts from './LowStockAlerts';

const Dashboard = () => {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState(null);
  const [recentActivities, setRecentActivities] = useState(null);
  const [alerts, setAlerts] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    baseId: '',
    assetType: '',
    startDate: '',
    endDate: ''
  });

  useEffect(() => {
    fetchDashboardData();
  }, [filters]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await dashboardAPI.getMetrics(filters);
      
      if (response.data.success) {
        const { metrics, recentActivities, alerts } = response.data.data;
        setMetrics(metrics);
        setRecentActivities(recentActivities);
        setAlerts(alerts);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-military-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">{error}</div>
          <button
            onClick={fetchDashboardData}
            className="btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="mt-2 text-sm text-gray-600">
              Welcome back, {user?.fullName || user?.firstName} ({user?.rank})
            </p>
            {user?.assignedBase && (
              <p className="text-sm text-gray-500">
                Base: {user.assignedBase.name} ({user.assignedBase.code})
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Filters */}
      <FiltersPanel 
        filters={filters} 
        onFilterChange={handleFilterChange}
        userRole={user?.role}
        userBase={user?.assignedBase}
      />

      {/* Metrics Cards */}
      {metrics && (
        <MetricsCards 
          metrics={metrics} 
          onNetMovementClick={() => {
            // TODO: Implement popup for movement details
            console.log('Show movement details popup');
          }}
        />
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        {recentActivities && (
          <RecentActivities activities={recentActivities} />
        )}

        {/* Low Stock Alerts */}
        {alerts && (
          <LowStockAlerts alerts={alerts.lowStockItems} />
        )}
      </div>
    </div>
  );
};

export default Dashboard;
