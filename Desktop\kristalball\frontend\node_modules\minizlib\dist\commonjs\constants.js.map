{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";;;;;;AAAA,0EAA0E;AAC1E,wEAAwE;AACxE,oEAAoE;AACpE,kEAAkE;AAClE,gDAA2B;AAC3B,qBAAqB;AACrB,MAAM,iBAAiB,GAAG,cAAQ,CAAC,SAAS,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAA;AACrE,oBAAoB;AAEP,QAAA,SAAS,GAAG,MAAM,CAAC,MAAM,CACpC,MAAM,CAAC,MAAM,CACX,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EACnB;IACE,UAAU,EAAE,CAAC;IACb,eAAe,EAAE,CAAC;IAClB,YAAY,EAAE,CAAC;IACf,YAAY,EAAE,CAAC;IACf,QAAQ,EAAE,CAAC;IACX,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,YAAY,EAAE,CAAC;IACf,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,CAAC,CAAC;IACX,cAAc,EAAE,CAAC,CAAC;IAClB,YAAY,EAAE,CAAC,CAAC;IAChB,WAAW,EAAE,CAAC,CAAC;IACf,WAAW,EAAE,CAAC,CAAC;IACf,eAAe,EAAE,CAAC,CAAC;IACnB,gBAAgB,EAAE,CAAC;IACnB,YAAY,EAAE,CAAC;IACf,kBAAkB,EAAE,CAAC;IACrB,qBAAqB,EAAE,CAAC,CAAC;IACzB,UAAU,EAAE,CAAC;IACb,cAAc,EAAE,CAAC;IACjB,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;IACV,kBAAkB,EAAE,CAAC;IACrB,OAAO,EAAE,CAAC;IACV,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,MAAM,EAAE,CAAC;IACT,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,CAAC;IACb,KAAK,EAAE,CAAC;IACR,aAAa,EAAE,CAAC;IAChB,aAAa,EAAE,CAAC;IAChB,gBAAgB,EAAE,CAAC;IACnB,gBAAgB,EAAE,EAAE;IACpB,oBAAoB,EAAE,EAAE;IACxB,WAAW,EAAE,EAAE;IACf,WAAW,EAAE,QAAQ;IACrB,eAAe,EAAE,KAAK;IACtB,cAAc,EAAE,CAAC;IACjB,cAAc,EAAE,CAAC;IACjB,kBAAkB,EAAE,CAAC;IACrB,WAAW,EAAE,CAAC,CAAC;IACf,WAAW,EAAE,CAAC;IACd,eAAe,EAAE,CAAC,CAAC;IACnB,wBAAwB,EAAE,CAAC;IAC3B,sBAAsB,EAAE,CAAC;IACzB,uBAAuB,EAAE,CAAC;IAC1B,8BAA8B,EAAE,CAAC;IACjC,mBAAmB,EAAE,CAAC;IACtB,gBAAgB,EAAE,CAAC;IACnB,gBAAgB,EAAE,CAAC;IACnB,mBAAmB,EAAE,CAAC;IACtB,kBAAkB,EAAE,CAAC;IACrB,kBAAkB,EAAE,EAAE;IACtB,sBAAsB,EAAE,EAAE;IAC1B,sBAAsB,EAAE,EAAE;IAC1B,sBAAsB,EAAE,EAAE;IAC1B,4BAA4B,EAAE,EAAE;IAChC,qBAAqB,EAAE,EAAE;IACzB,2BAA2B,EAAE,EAAE;IAC/B,2BAA2B,EAAE,EAAE;IAC/B,iBAAiB,EAAE,CAAC;IACpB,oBAAoB,EAAE,CAAC;IACvB,kBAAkB,EAAE,CAAC;IACrB,oBAAoB,EAAE,CAAC;IACvB,6CAA6C,EAAE,CAAC;IAChD,sBAAsB,EAAE,CAAC;IACzB,yBAAyB,EAAE,CAAC;IAC5B,qBAAqB,EAAE,CAAC;IACxB,oBAAoB,EAAE,CAAC;IACvB,2BAA2B,EAAE,CAAC;IAC9B,6BAA6B,EAAE,CAAC;IAChC,sCAAsC,EAAE,CAAC;IACzC,uCAAuC,EAAE,CAAC;IAC1C,qDAAqD,EAAE,CAAC;IACxD,iCAAiC,EAAE,CAAC;IACpC,uBAAuB,EAAE,CAAC;IAC1B,sBAAsB,EAAE,CAAC;IACzB,+BAA+B,EAAE,CAAC;IAClC,gCAAgC,EAAE,CAAC;IACnC,4CAA4C,EAAE,CAAC,CAAC;IAChD,oCAAoC,EAAE,CAAC,CAAC;IACxC,iDAAiD,EAAE,CAAC,CAAC;IACrD,mDAAmD,EAAE,CAAC,CAAC;IACvD,+CAA+C,EAAE,CAAC,CAAC;IACnD,oCAAoC,EAAE,CAAC,CAAC;IACxC,yCAAyC,EAAE,CAAC,CAAC;IAC7C,8CAA8C,EAAE,CAAC,CAAC;IAClD,0CAA0C,EAAE,CAAC,CAAC;IAC9C,0CAA0C,EAAE,CAAC,EAAE;IAC/C,qCAAqC,EAAE,CAAC,EAAE;IAC1C,sCAAsC,EAAE,CAAC,EAAE;IAC3C,uCAAuC,EAAE,CAAC,EAAE;IAC5C,qCAAqC,EAAE,CAAC,EAAE;IAC1C,qCAAqC,EAAE,CAAC,EAAE;IAC1C,oCAAoC,EAAE,CAAC,EAAE;IACzC,uCAAuC,EAAE,CAAC,EAAE;IAC5C,sCAAsC,EAAE,CAAC,EAAE;IAC3C,wCAAwC,EAAE,CAAC,EAAE;IAC7C,sCAAsC,EAAE,CAAC,EAAE;IAC3C,sCAAsC,EAAE,CAAC,EAAE;IAC3C,wCAAwC,EAAE,CAAC,EAAE;IAC7C,wCAAwC,EAAE,CAAC,EAAE;IAC7C,2CAA2C,EAAE,CAAC,EAAE;IAChD,gCAAgC,EAAE,CAAC,EAAE;CACtC,EACD,iBAAiB,CAClB,CACF,CAAA", "sourcesContent": ["// Update with any zlib constants that are added or changed in the future.\n// Node v6 didn't export this, so we just hard code the version and rely\n// on all the other hard-coded values from zlib v4736.  When node v6\n// support drops, we can just export the realZlibConstants object.\nimport realZlib from 'zlib'\n/* c8 ignore start */\nconst realZlibConstants = realZlib.constants || { ZLIB_VERNUM: 4736 }\n/* c8 ignore stop */\n\nexport const constants = Object.freeze(\n  Object.assign(\n    Object.create(null),\n    {\n      Z_NO_FLUSH: 0,\n      Z_PARTIAL_FLUSH: 1,\n      Z_SYNC_FLUSH: 2,\n      Z_FULL_FLUSH: 3,\n      Z_FINISH: 4,\n      Z_BLOCK: 5,\n      Z_OK: 0,\n      Z_STREAM_END: 1,\n      Z_NEED_DICT: 2,\n      Z_ERRNO: -1,\n      Z_STREAM_ERROR: -2,\n      Z_DATA_ERROR: -3,\n      Z_MEM_ERROR: -4,\n      Z_BUF_ERROR: -5,\n      Z_<PERSON>ERSION_ERROR: -6,\n      Z_NO_COMPRESSION: 0,\n      Z_BEST_SPEED: 1,\n      Z_BEST_COMPRESSION: 9,\n      Z_DEFAULT_COMPRESSION: -1,\n      Z_FILTERED: 1,\n      Z_HUFFMAN_ONLY: 2,\n      Z_RLE: 3,\n      Z_FIXED: 4,\n      Z_DEFAULT_STRATEGY: 0,\n      DEFLATE: 1,\n      INFLATE: 2,\n      GZIP: 3,\n      GUNZIP: 4,\n      DEFLATERAW: 5,\n      INFLATERAW: 6,\n      UNZIP: 7,\n      BROTLI_DECODE: 8,\n      BROTLI_ENCODE: 9,\n      Z_MIN_WINDOWBITS: 8,\n      Z_MAX_WINDOWBITS: 15,\n      Z_DEFAULT_WINDOWBITS: 15,\n      Z_MIN_CHUNK: 64,\n      Z_MAX_CHUNK: Infinity,\n      Z_DEFAULT_CHUNK: 16384,\n      Z_MIN_MEMLEVEL: 1,\n      Z_MAX_MEMLEVEL: 9,\n      Z_DEFAULT_MEMLEVEL: 8,\n      Z_MIN_LEVEL: -1,\n      Z_MAX_LEVEL: 9,\n      Z_DEFAULT_LEVEL: -1,\n      BROTLI_OPERATION_PROCESS: 0,\n      BROTLI_OPERATION_FLUSH: 1,\n      BROTLI_OPERATION_FINISH: 2,\n      BROTLI_OPERATION_EMIT_METADATA: 3,\n      BROTLI_MODE_GENERIC: 0,\n      BROTLI_MODE_TEXT: 1,\n      BROTLI_MODE_FONT: 2,\n      BROTLI_DEFAULT_MODE: 0,\n      BROTLI_MIN_QUALITY: 0,\n      BROTLI_MAX_QUALITY: 11,\n      BROTLI_DEFAULT_QUALITY: 11,\n      BROTLI_MIN_WINDOW_BITS: 10,\n      BROTLI_MAX_WINDOW_BITS: 24,\n      BROTLI_LARGE_MAX_WINDOW_BITS: 30,\n      BROTLI_DEFAULT_WINDOW: 22,\n      BROTLI_MIN_INPUT_BLOCK_BITS: 16,\n      BROTLI_MAX_INPUT_BLOCK_BITS: 24,\n      BROTLI_PARAM_MODE: 0,\n      BROTLI_PARAM_QUALITY: 1,\n      BROTLI_PARAM_LGWIN: 2,\n      BROTLI_PARAM_LGBLOCK: 3,\n      BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING: 4,\n      BROTLI_PARAM_SIZE_HINT: 5,\n      BROTLI_PARAM_LARGE_WINDOW: 6,\n      BROTLI_PARAM_NPOSTFIX: 7,\n      BROTLI_PARAM_NDIRECT: 8,\n      BROTLI_DECODER_RESULT_ERROR: 0,\n      BROTLI_DECODER_RESULT_SUCCESS: 1,\n      BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT: 2,\n      BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT: 3,\n      BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION: 0,\n      BROTLI_DECODER_PARAM_LARGE_WINDOW: 1,\n      BROTLI_DECODER_NO_ERROR: 0,\n      BROTLI_DECODER_SUCCESS: 1,\n      BROTLI_DECODER_NEEDS_MORE_INPUT: 2,\n      BROTLI_DECODER_NEEDS_MORE_OUTPUT: 3,\n      BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE: -1,\n      BROTLI_DECODER_ERROR_FORMAT_RESERVED: -2,\n      BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE: -3,\n      BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET: -4,\n      BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME: -5,\n      BROTLI_DECODER_ERROR_FORMAT_CL_SPACE: -6,\n      BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE: -7,\n      BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT: -8,\n      BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1: -9,\n      BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2: -10,\n      BROTLI_DECODER_ERROR_FORMAT_TRANSFORM: -11,\n      BROTLI_DECODER_ERROR_FORMAT_DICTIONARY: -12,\n      BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS: -13,\n      BROTLI_DECODER_ERROR_FORMAT_PADDING_1: -14,\n      BROTLI_DECODER_ERROR_FORMAT_PADDING_2: -15,\n      BROTLI_DECODER_ERROR_FORMAT_DISTANCE: -16,\n      BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET: -19,\n      BROTLI_DECODER_ERROR_INVALID_ARGUMENTS: -20,\n      BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES: -21,\n      BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS: -22,\n      BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP: -25,\n      BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1: -26,\n      BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2: -27,\n      BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES: -30,\n      BROTLI_DECODER_ERROR_UNREACHABLE: -31,\n    },\n    realZlibConstants,\n  ),\n)\n"]}