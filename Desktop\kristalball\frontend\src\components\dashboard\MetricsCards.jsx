import { 
  CubeIcon, 
  ArrowTrendingUpIcon, 
  ArrowTrendingDownIcon,
  UserGroupIcon,
  FireIcon
} from '@heroicons/react/24/outline';

const MetricsCards = ({ metrics, onNetMovementClick }) => {
  const cards = [
    {
      title: 'Opening Balance',
      value: metrics.openingBalance.toLocaleString(),
      icon: CubeIcon,
      color: 'bg-blue-500',
      description: 'Starting inventory count'
    },
    {
      title: 'Closing Balance',
      value: metrics.closingBalance.toLocaleString(),
      icon: CubeIcon,
      color: 'bg-green-500',
      description: 'Current inventory count'
    },
    {
      title: 'Net Movement',
      value: metrics.netMovement.toLocaleString(),
      icon: metrics.netMovement >= 0 ? ArrowTrendingUpIcon : ArrowTrendingDownIcon,
      color: metrics.netMovement >= 0 ? 'bg-emerald-500' : 'bg-red-500',
      description: 'Purchases + Transfers In - Transfers Out',
      clickable: true,
      onClick: onNetMovementClick
    },
    {
      title: 'Current Stock',
      value: metrics.currentStock.toLocaleString(),
      icon: CubeIcon,
      color: 'bg-purple-500',
      description: 'Available inventory'
    },
    {
      title: 'Total Assigned',
      value: metrics.totalAssigned.toLocaleString(),
      icon: UserGroupIcon,
      color: 'bg-orange-500',
      description: 'Assets assigned to personnel'
    },
    {
      title: 'Total Expended',
      value: metrics.totalExpended.toLocaleString(),
      icon: FireIcon,
      color: 'bg-red-500',
      description: 'Assets consumed/used'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cards.map((card, index) => (
        <div
          key={index}
          className={`card p-6 ${card.clickable ? 'cursor-pointer hover:shadow-lg transition-shadow' : ''}`}
          onClick={card.clickable ? card.onClick : undefined}
        >
          <div className="flex items-center">
            <div className={`flex-shrink-0 ${card.color} rounded-md p-3`}>
              <card.icon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  {card.title}
                  {card.clickable && (
                    <span className="ml-1 text-xs text-blue-600">(click for details)</span>
                  )}
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {card.value}
                </dd>
                <dd className="text-xs text-gray-400 mt-1">
                  {card.description}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MetricsCards;
