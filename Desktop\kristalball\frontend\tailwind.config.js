/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        military: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        olive: {
          50: '#f7f8f0',
          100: '#eef0de',
          200: '#dde2c0',
          300: '#c6cf9a',
          400: '#b0bc77',
          500: '#9ba05c',
          600: '#7a7f47',
          700: '#5f623a',
          800: '#4e5032',
          900: '#43442d',
        }
      },
      fontFamily: {
        'military': ['Inter', 'system-ui', 'sans-serif'],
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
